#!/usr/bin/env python
"""
Simple performance test script for product detail page optimization.
Run this script to test the query count reduction.

Usage:
    python test_performance.py
"""

import os
import sys
import django
from django.conf import settings

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from django.db import connection
from django.test import RequestFactory
from django.contrib.auth.models import AnonymousUser
from project.apps.catalogue.models import Product
from project.apps.catalogue.views import ProductDetailView


def test_product_detail_optimization():
    """Test the product detail page optimization"""
    
    print("=== Product Detail Page Optimization Test ===\n")
    
    # Get a test product
    try:
        product = Product.objects.get(id=83359)
        print(f"Testing with product: {product.get_title()}")
    except Product.DoesNotExist:
        print("Test product with ID 83359 not found. Using first available product.")
        product = Product.objects.first()
        if not product:
            print("No products found in database!")
            return
    
    print(f"Product ID: {product.id}")
    print(f"Product title: {product.get_title()}\n")
    
    # Test 1: Model method calls (cached properties)
    print("=== Test 1: Model Method Calls (Cached Properties) ===")
    
    # Fresh product instance to clear cache
    product_fresh = Product.objects.get(id=product.id)
    
    # Clear query log
    connection.queries_log.clear()
    
    # First calls (should make queries)
    manufacturer = product_fresh.get_main_manufacturer()
    model_cleaned = product_fresh.get_main_model_cleaned()
    year = product_fresh.get_main_year()
    category = product_fresh.get_main_category()
    
    first_call_queries = len(connection.queries)
    print(f"First calls - Queries made: {first_call_queries}")
    print(f"Results: {manufacturer}, {model_cleaned}, {year}, {category}")
    
    # Clear query log
    connection.queries_log.clear()
    
    # Second calls (should use cache)
    manufacturer2 = product_fresh.get_main_manufacturer()
    model_cleaned2 = product_fresh.get_main_model_cleaned()
    year2 = product_fresh.get_main_year()
    category2 = product_fresh.get_main_category()
    
    second_call_queries = len(connection.queries)
    print(f"Second calls - Queries made: {second_call_queries}")
    print(f"Results: {manufacturer2}, {model_cleaned2}, {year2}, {category2}")
    
    if second_call_queries == 0:
        print("✅ Cached properties working correctly!")
    else:
        print("❌ Cached properties not working - still making queries")
    
    print()
    
    # Test 2: View context preparation
    print("=== Test 2: View Context Preparation ===")
    
    # Create mock request
    factory = RequestFactory()
    request = factory.get(f'/en/catalogue/test-product_{product.id}/')
    request.user = AnonymousUser()
    
    # Mock branch and strategy
    class MockBranch:
        branch = 'eu'
        country = 'LT'

    class MockStrategy:
        def fetch_for_product(self, product):
            class MockPurchaseInfo:
                class MockPrice:
                    exists = True
                    is_tax_known = True
                    incl_tax = 25.99
                class MockAvailability:
                    is_available_to_buy = True
                class MockStockRecord:
                    num_in_stock = 10
                price = MockPrice()
                availability = MockAvailability()
                stockrecord = MockStockRecord()
            return MockPurchaseInfo()
        
        def discount_for_product(self, product):
            class MockDiscountInfo:
                has_discount = False
            return MockDiscountInfo()

    request.branch = MockBranch()
    request.strategy = MockStrategy()
    
    # Test view context preparation
    view = ProductDetailView()
    view.request = request
    view.object = product
    
    # Clear query log
    connection.queries_log.clear()
    
    try:
        context = view.get_context_data()
        context_queries = len(connection.queries)
        
        print(f"View context preparation - Queries made: {context_queries}")
        print(f"Context keys: {list(context.keys())}")
        
        # Check if optimized data is in context
        optimized_keys = ['main_manufacturer', 'main_model_cleaned', 'main_year', 'main_category', 'purchase_info', 'discount_info']
        missing_keys = [key for key in optimized_keys if key not in context]
        
        if not missing_keys:
            print("✅ All optimized context data present!")
        else:
            print(f"❌ Missing context keys: {missing_keys}")
            
        if context_queries < 15:  # Should be much less than original 57
            print(f"✅ Query count is optimized! ({context_queries} queries)")
        else:
            print(f"❌ Query count still high: {context_queries} queries")
            
    except Exception as e:
        print(f"❌ Error testing view context: {e}")
    
    print()
    
    # Summary
    print("=== Summary ===")
    print(f"Model cached properties: {'✅ Working' if second_call_queries == 0 else '❌ Not working'}")
    print(f"View optimization: {'✅ Working' if context_queries < 15 else '❌ Needs improvement'}")
    print(f"Total context queries: {context_queries} (target: <15, original: ~57)")
    
    improvement = ((57 - context_queries) / 57) * 100 if context_queries < 57 else 0
    print(f"Query reduction: {improvement:.1f}%")
    
    if improvement > 70:
        print("🎉 Excellent optimization!")
    elif improvement > 50:
        print("👍 Good optimization!")
    elif improvement > 30:
        print("👌 Moderate optimization!")
    else:
        print("⚠️  Optimization needs improvement!")


if __name__ == "__main__":
    test_product_detail_optimization()
