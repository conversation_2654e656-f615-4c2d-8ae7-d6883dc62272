/**
 * DOM Analyzer Tool
 *
 * This tool helps identify which parts of your page generate the most DOM elements.
 * It adds a floating panel that shows element counts for different sections of the page.
 *
 * IMPORTANT: This tool is for development use only and may impact page performance.
 * Initialize manually by calling DOMAnalyzer.init() when needed.
 */

const DOMAnalyzer = {
    // Configuration
    config: {
        // CSS selectors for important page sections to analyze
        sections: [
            { selector: 'body', name: 'Entire Page' },
            { selector: 'aside', name: 'Sidebar' },
            { selector: 'main', name: 'Main Content' },
            { selector: '#category-tree-container', name: 'Category Tree Container' },
            { selector: '#products-container', name: 'Products Container' },
            { selector: '#extra-attributes-container', name: 'Extra Attributes' },
            { selector: '#range-filters-container', name: 'Range Filters' },
            { selector: '[x-data="productGalleryModal"]', name: 'Product Gallery Modal' },
            // Vehicle filter and navigation
            { selector: '.bg-gray-100.p-4.rounded-lg.mb-6', name: 'Vehicle Filter' },
            { selector: '#manufacturer', name: 'Vehicle Manufacturer Dropdown' },
            { selector: '#model-container', name: 'Vehicle Model Container' },
            { selector: '#type-container', name: 'Vehicle Type Container' },
            { selector: 'nav.bg-black', name: 'Navigation Bar' },
            { selector: 'nav.bg-black .lg\\:hidden', name: 'Mobile Navigation' },
            { selector: 'nav.bg-black .hidden.lg\\:flex', name: 'Desktop Navigation' },
            { selector: '#mobile-menu', name: 'Mobile Menu' },
            // Alpine.js components
            { selector: '[x-data="productCard"]', name: 'Product Card Component' },
            { selector: 'template', name: 'Template Elements' },
        ],
        // Highlight color for sections
        highlightColor: 'rgba(255, 0, 0, 0.2)',
        // Panel position
        panelPosition: 'top-right',
        // Performance settings
        debounceTime: 500 // ms to wait before processing DOM changes
    },

    // State
    state: {
        isInitialized: false,
        isPanelVisible: true,
        highlightedElement: null,
        highlightTimer: null,
        debounceTimer: null,
        observer: null,
        isAnalyzing: false,
    },

    /**
     * Initialize the DOM Analyzer
     * @param {boolean} enableAutoUpdate - Whether to enable automatic updates on DOM changes
     */
    init: function(enableAutoUpdate = false) {
        if (this.state.isInitialized) return;

        this.createPanel();
        this.findCustomSections();
        this.analyzeDOM();
        this.addToggleButton();

        // Only set up the observer if auto-update is enabled
        if (enableAutoUpdate) {
            this.setupObserver();
        }

        this.state.isInitialized = true;
        console.log('DOM Analyzer initialized' + (enableAutoUpdate ? ' with auto-updates' : ' (manual mode)'));

        // Make DOMAnalyzer globally accessible
        window.DOMAnalyzer = this;
    },

    /**
     * Set up the MutationObserver with debouncing
     */
    setupObserver: function() {
        // Clean up existing observer if any
        if (this.state.observer) {
            this.state.observer.disconnect();
        }

        // Create new observer with debounced callback
        this.state.observer = new MutationObserver(() => {
            // Clear any existing timer
            if (this.state.debounceTimer) {
                clearTimeout(this.state.debounceTimer);
            }

            // Set a new timer
            this.state.debounceTimer = setTimeout(() => {
                // Only analyze if not already analyzing
                if (!this.state.isAnalyzing) {
                    this.analyzeDOM();
                }
            }, this.config.debounceTime);
        });

        // Start observing with limited scope
        this.state.observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: false,
            characterData: false
        });

        console.log('DOM Observer set up with debounce time of ' + this.config.debounceTime + 'ms');
    },

    /**
     * Stop the observer to prevent further automatic updates
     */
    stopObserver: function() {
        if (this.state.observer) {
            this.state.observer.disconnect();
            this.state.observer = null;
            console.log('DOM Observer stopped');
        }
    },

    /**
     * Find custom sections based on data attributes and Alpine.js components
     * Optimized to limit the number of components analyzed
     */
    findCustomSections: function() {
        this.config.customSections = [];

        try {
            // Find Alpine.js components (limit to first 20 to prevent performance issues)
            const alpineComponents = document.querySelectorAll('[x-data]');
            const maxComponents = Math.min(alpineComponents.length, 20);

            // Process only a limited number of components
            for (let i = 0; i < maxComponents; i++) {
                const el = alpineComponents[i];
                const dataAttr = el.getAttribute('x-data');

                // Skip empty data attributes
                if (!dataAttr) continue;

                this.config.customSections.push({
                    element: el,
                    selector: `[x-data="${dataAttr}"]`,
                    name: `Alpine: ${dataAttr}`
                });
            }

            // Find template sections (limit to first 10)
            const templates = document.querySelectorAll('template');
            const maxTemplates = Math.min(templates.length, 10);

            for (let i = 0; i < maxTemplates; i++) {
                this.config.customSections.push({
                    element: templates[i],
                    selector: `template:nth-of-type(${i + 1})`,
                    name: `Template ${i + 1}`
                });
            }

            console.log(`Found ${this.config.customSections.length} custom sections`);
        } catch (error) {
            console.error('Error finding custom sections:', error);
        }
    },

    /**
     * Create the floating panel
     */
    createPanel: function() {
        // Create panel container
        const panel = document.createElement('div');
        panel.id = 'dom-analyzer-panel';
        panel.style.cssText = `
            position: fixed;
            ${this.config.panelPosition === 'top-right' ? 'top: 10px; right: 10px;' : 'bottom: 10px; right: 10px;'}
            width: 300px;
            max-height: 80vh;
            overflow-y: auto;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            border-radius: 5px;
            padding: 10px;
            font-family: monospace;
            z-index: 10000;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
        `;

        // Create panel header
        const header = document.createElement('div');
        header.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
        `;

        const title = document.createElement('h3');
        title.textContent = 'DOM Analyzer';
        title.style.margin = '0';

        const closeButton = document.createElement('button');
        closeButton.textContent = 'X';
        closeButton.style.cssText = `
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-weight: bold;
        `;
        closeButton.onclick = () => this.togglePanel();

        header.appendChild(title);
        header.appendChild(closeButton);
        panel.appendChild(header);

        // Create content container
        const content = document.createElement('div');
        content.id = 'dom-analyzer-content';
        panel.appendChild(content);

        // Add refresh button
        const refreshButton = document.createElement('button');
        refreshButton.textContent = 'Refresh Analysis';
        refreshButton.style.cssText = `
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 5px 10px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 12px;
            margin: 10px 0 0 0;
            cursor: pointer;
            border-radius: 3px;
            width: 100%;
        `;
        refreshButton.onclick = () => this.analyzeDOM();
        panel.appendChild(refreshButton);

        document.body.appendChild(panel);
    },

    /**
     * Add toggle button to show/hide the panel
     */
    addToggleButton: function() {
        const button = document.createElement('button');
        button.id = 'dom-analyzer-toggle';
        button.textContent = 'DOM';
        button.style.cssText = `
            position: fixed;
            ${this.config.panelPosition === 'top-right' ? 'top: 10px; right: 320px;' : 'bottom: 10px; right: 320px;'}
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 5px 10px;
            cursor: pointer;
            z-index: 10000;
            display: none;
        `;
        button.onclick = () => this.togglePanel();
        document.body.appendChild(button);
    },

    /**
     * Toggle panel visibility
     */
    togglePanel: function() {
        const panel = document.getElementById('dom-analyzer-panel');
        const button = document.getElementById('dom-analyzer-toggle');

        if (this.state.isPanelVisible) {
            panel.style.display = 'none';
            button.style.display = 'block';
        } else {
            panel.style.display = 'block';
            button.style.display = 'none';
        }

        this.state.isPanelVisible = !this.state.isPanelVisible;
    },

    /**
     * Analyze the DOM and update the panel
     * Optimized to prevent excessive DOM operations
     */
    analyzeDOM: async function() {
        const content = document.getElementById('dom-analyzer-content');
        if (!content) return;

        // Set analyzing flag to prevent concurrent analysis
        if (this.state.isAnalyzing) {
            console.log('Analysis already in progress, skipping');
            return;
        }

        try {
            this.state.isAnalyzing = true;
            console.time('DOM Analysis');

            content.innerHTML = '';

            // Add timestamp
            const timestamp = document.createElement('div');
            timestamp.style.cssText = `
                font-size: 10px;
                color: #aaa;
                text-align: right;
                margin-bottom: 5px;
            `;
            timestamp.textContent = `Last updated: ${new Date().toLocaleTimeString()}`;
            content.appendChild(timestamp);

            // Analyze predefined sections
            let sectionsAnalyzed = 0;
            for (const section of this.config.sections) {
                try {
                    const elements = document.querySelectorAll(section.selector);
                    if (elements.length > 0) {
                        this.addSectionToPanel(content, section.name, elements[0]);
                        sectionsAnalyzed++;
                    }
                } catch (error) {
                    console.error(`Error analyzing section ${section.name}:`, error);
                }

                // Check if we've exceeded our time budget (50ms)
                if (performance.now() % 100 < 50) {
                    // Give the browser a chance to breathe
                    await new Promise(resolve => setTimeout(resolve, 0));
                }
            }

            // Add separator
            const separator = document.createElement('hr');
            separator.style.cssText = `
                border: none;
                border-top: 1px dashed rgba(255, 255, 255, 0.3);
                margin: 10px 0;
            `;
            content.appendChild(separator);

            // Analyze custom sections
            let customSectionsAnalyzed = 0;
            for (const section of this.config.customSections) {
                if (section.element) {
                    try {
                        this.addSectionToPanel(content, section.name, section.element);
                        customSectionsAnalyzed++;
                    } catch (error) {
                        console.error(`Error analyzing custom section ${section.name}:`, error);
                    }
                }

                // Check if we've exceeded our time budget (50ms)
                if (performance.now() % 100 < 50) {
                    // Give the browser a chance to breathe
                    await new Promise(resolve => setTimeout(resolve, 0));
                }
            }

            console.log(`Analyzed ${sectionsAnalyzed} predefined sections and ${customSectionsAnalyzed} custom sections`);
            console.timeEnd('DOM Analysis');
        } catch (error) {
            console.error('Error during DOM analysis:', error);
        } finally {
            this.state.isAnalyzing = false;
        }
    },

    /**
     * Add a section to the panel
     */
    addSectionToPanel: function(container, name, element) {
        const elementCount = this.countElements(element);
        const textCount = this.countTextNodes(element);

        const row = document.createElement('div');
        row.className = 'dom-analyzer-row';
        row.style.cssText = `
            display: flex;
            justify-content: space-between;
            padding: 5px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
        `;
        row.onclick = () => this.highlightElement(element);

        const nameSpan = document.createElement('span');
        nameSpan.textContent = name;
        nameSpan.style.cssText = `
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        `;

        const countSpan = document.createElement('span');
        countSpan.textContent = `${elementCount} elements`;
        countSpan.style.cssText = `
            margin-left: 10px;
            font-weight: bold;
            color: ${elementCount > 1000 ? '#ff6b6b' : elementCount > 500 ? '#ffa94d' : '#69db7c'};
        `;

        row.appendChild(nameSpan);
        row.appendChild(countSpan);
        container.appendChild(row);

        // Add details row
        const detailsRow = document.createElement('div');
        detailsRow.style.cssText = `
            padding: 5px 5px 5px 15px;
            font-size: 11px;
            color: rgba(255, 255, 255, 0.7);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        `;

        const tagCounts = this.countTagTypes(element);
        const tagDetails = Object.entries(tagCounts)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5)
            .map(([tag, count]) => `${tag}: ${count}`)
            .join(', ');

        detailsRow.textContent = `Text nodes: ${textCount}, Top tags: ${tagDetails}`;
        container.appendChild(detailsRow);
    },

    /**
     * Count all elements within a container (no limits)
     */
    countElements: function(element) {
        try {
            // Count all elements without limits
            const elements = element.getElementsByTagName('*');

            // Log large element counts to console for debugging
            if (elements.length > 10000) {
                console.warn(`Large element count: ${elements.length} elements in ${element.tagName}`);
            }

            return elements.length + 1; // +1 for the element itself
        } catch (error) {
            console.error('Error counting elements:', error);
            return 'Error';
        }
    },

    /**
     * Count text nodes within a container (no limits)
     */
    countTextNodes: function(element) {
        try {
            let count = 0;
            let nodesProcessed = 0;

            const walker = document.createTreeWalker(
                element,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            while (walker.nextNode()) {
                nodesProcessed++;
                if (walker.currentNode.nodeValue.trim() !== '') {
                    count++;
                }

                // Every 5000 nodes, give the browser a chance to breathe
                if (nodesProcessed % 5000 === 0) {
                    console.log(`Processed ${nodesProcessed} text nodes so far...`);
                }
            }

            // Log large text node counts to console for debugging
            if (nodesProcessed > 5000) {
                console.warn(`Large text node count: ${count} text nodes in ${element.tagName}`);
            }

            return count;
        } catch (error) {
            console.error('Error counting text nodes:', error);
            return 'Error';
        }
    },

    /**
     * Count occurrences of each tag type (no limits)
     */
    countTagTypes: function(element) {
        try {
            const counts = {};
            const elements = element.getElementsByTagName('*');
            const totalElements = elements.length;

            // Process all elements without limits
            for (let i = 0; i < totalElements; i++) {
                const tagName = elements[i].tagName.toLowerCase();
                counts[tagName] = (counts[tagName] || 0) + 1;

                // Every 10000 elements, log progress
                if (i > 0 && i % 10000 === 0) {
                    console.log(`Processed ${i}/${totalElements} elements for tag counting...`);
                }
            }

            // Log large element counts to console for debugging
            if (totalElements > 10000) {
                console.warn(`Counted tags for ${totalElements} elements in ${element.tagName}`);
            }

            return counts;
        } catch (error) {
            console.error('Error counting tag types:', error);
            return {};
        }
    },

    /**
     * Highlight an element in the page
     */
    highlightElement: function(element) {
        // Clear previous highlight
        if (this.state.highlightedElement) {
            this.state.highlightedElement.style.backgroundColor = '';
            this.state.highlightedElement.style.outline = '';
        }

        // Clear previous timer
        if (this.state.highlightTimer) {
            clearTimeout(this.state.highlightTimer);
        }

        // Highlight new element
        const originalBgColor = element.style.backgroundColor;
        const originalOutline = element.style.outline;

        element.style.backgroundColor = this.config.highlightColor;
        element.style.outline = `2px solid red`;

        // Scroll to element
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });

        this.state.highlightedElement = element;

        // Clear highlight after 3 seconds
        this.state.highlightTimer = setTimeout(() => {
            element.style.backgroundColor = originalBgColor;
            element.style.outline = originalOutline;
            this.state.highlightedElement = null;
        }, 3000);
    }
};

// Make the DOM Analyzer available globally but don't initialize automatically
window.DOMAnalyzer = DOMAnalyzer;

// Add a console message to inform how to use the tool
console.log(
    '%c DOM Analyzer is available! %c\n' +
    'Call DOMAnalyzer.init() to start analysis.\n' +
    'Call DOMAnalyzer.init(true) to enable auto-updates (may impact performance).\n' +
    'This version shows ALL elements without limits - may be slow on large pages!',
    'background: #4CAF50; color: white; font-weight: bold; padding: 2px 5px; border-radius: 3px;',
    'font-style: italic;'
);
