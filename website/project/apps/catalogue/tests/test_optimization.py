from django.test import TestCase, RequestFactory
from django.contrib.auth.models import AnonymousUser
from django.db import connection
from django.test.utils import override_settings

from project.apps.catalogue.views import ProductDetailView
from project.apps.catalogue.models import Product


class ProductDetailOptimizationTest(TestCase):
    """Test that the product detail view optimizations work correctly"""
    
    def setUp(self):
        self.factory = RequestFactory()
        
    def test_product_detail_query_optimization(self):
        """Test that the optimized view uses fewer queries"""
        # Get a product that exists in the database
        try:
            product = Product.objects.get(id=83359)
        except Product.DoesNotExist:
            self.skipTest("Test product with ID 83359 does not exist")
        
        # Create a test request
        request = self.factory.get(f'/en/catalogue/test-product_{product.id}/')
        request.user = AnonymousUser()
        
        # Mock the branch and strategy
        class MockBranch:
            branch = 'eu'
            country = 'LT'

        class MockStrategy:
            def fetch_for_product(self, product):
                class MockPurchaseInfo:
                    class MockPrice:
                        exists = True
                        is_tax_known = True
                        incl_tax = 25.99
                    class MockAvailability:
                        is_available_to_buy = True
                    class MockStockRecord:
                        num_in_stock = 10
                    price = MockPrice()
                    availability = MockAvailability()
                    stockrecord = MockStockRecord()
                return MockPurchaseInfo()
            
            def discount_for_product(self, product):
                class MockDiscountInfo:
                    has_discount = False
                return MockDiscountInfo()

        request.branch = MockBranch()
        request.strategy = MockStrategy()
        
        # Test the view
        view = ProductDetailView()
        view.request = request
        view.object = product
        
        # Clear queries and test context preparation
        with self.assertNumQueries(7):  # Should be much less than 57
            context = view.get_context_data()
            
            # Verify that the context contains the optimized data
            self.assertIn('main_manufacturer', context)
            self.assertIn('main_model_cleaned', context)
            self.assertIn('main_year', context)
            self.assertIn('main_category', context)
            self.assertIn('dpd_cash_countries', context)
            self.assertIn('purchase_info', context)
            self.assertIn('discount_info', context)
            
    def test_cached_properties_work(self):
        """Test that cached properties reduce database queries"""
        try:
            product = Product.objects.get(id=83359)
        except Product.DoesNotExist:
            self.skipTest("Test product with ID 83359 does not exist")
        
        # Clear the cache by getting a fresh instance
        product = Product.objects.get(id=83359)
        
        # First call should make queries
        with self.assertNumQueries(2):  # One for tecpap attributes, one for category
            manufacturer1 = product.get_main_manufacturer()
            model1 = product.get_main_model_cleaned()
            year1 = product.get_main_year()
            category1 = product.get_main_category()
        
        # Subsequent calls should use cached data
        with self.assertNumQueries(0):
            manufacturer2 = product.get_main_manufacturer()
            model2 = product.get_main_model_cleaned()
            year2 = product.get_main_year()
            category2 = product.get_main_category()
            
        # Results should be the same
        self.assertEqual(manufacturer1, manufacturer2)
        self.assertEqual(model1, model2)
        self.assertEqual(year1, year2)
        self.assertEqual(category1, category2)
