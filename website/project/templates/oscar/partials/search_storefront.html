{% load i18n %}

<form method="get" action="{% url 'search:search' %}" class="w-full lg:w-3/4 ml-auto lg:mr-4">
    <div class="relative" data-ac_url="{% url 'customer:ac_search_suggestions' %}" x-data="{ searchText: '' }">
        <span role="status" aria-live="polite" class="sr-only"></span>
        <input
            class="w-full px-4 py-2 rounded-lg bg-gray-700 text-white placeholder-gray-300 border-none focus:outline-none focus:ring-2 focus:ring-gray-500"
            id="id_q"
            name="q"
            placeholder="{% trans 'Search for parts' %}"
            spellcheck="false"
            tabindex="1"
            type="search"
            autocomplete="off"
            x-model="searchText"
            x-ref="searchInput">
        <div id="ac_container" class="absolute w-full z-10"></div>

        <!-- Clear button (X) - only shows when there's text -->
        <button
            x-cloak
            x-bind:class="searchText.length > 0 ? 'absolute right-10 top-0 bottom-0 my-auto h-full p-0 m-0 w-8 bg-transparent border-0 text-gray-300 hover:text-white transition flex items-center justify-center' : 'hidden'"
            @click="searchText = ''; $nextTick(() => $refs.searchInput.focus())"
            type="button"
            tabindex="-1"
            aria-label="Clear">
            <!-- Heroicons x-mark icon -->
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-5 h-5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>

        <!-- Search button -->
        <button type="submit" class="absolute right-0 inset-y-0 flex items-center justify-center w-10 text-gray-300 hover:text-white" aria-label="{% trans 'Search' %}">
            <!-- Heroicons search icon -->
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
            </svg>
        </button>
    </div>
</form>
