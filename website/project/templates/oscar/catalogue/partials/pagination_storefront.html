{% load display_tags %}
{% load i18n %}

{% if is_paginated %}
<div class="flex justify-center my-6" x-cloak>
    <nav class="inline-flex rounded-md shadow-sm -space-x-px" aria-label="{% trans 'Pagination' %}">
        {% if page_obj.has_previous %}
            <button
                hx-get="{{ page_url }}1"
                hx-target="#products-container"
                hx-swap="innerHTML"
                hx-push-url="true"
                hx-history="false"
                hx-indicator=".htmx-indicator"
                hx-on:htmx:before-request=" document.getElementById('products-container').scrollIntoView({behavior: 'smooth'});"
                class="relative inline-flex items-center px-3 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
                aria-label="{% trans 'First page' %}">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                    <path fill-rule="evenodd" d="M13.28 3.97a.75.75 0 010 1.06L6.31 12l6.97 6.97a.75.75 0 11-1.06 1.06l-7.5-7.5a.75.75 0 010-1.06l7.5-7.5a.75.75 0 011.06 0zm6 0a.75.75 0 010 1.06L12.31 12l6.97 6.97a.75.75 0 11-1.06 1.06l-7.5-7.5a.75.75 0 010-1.06l7.5-7.5a.75.75 0 011.06 0z" clip-rule="evenodd" />
                </svg>
                <span class="sr-only">{% trans 'First page' %}</span>
            </button>
            <button
                hx-get="{{ page_url }}{{ page_obj.previous_page_number }}"
                hx-target="#products-container"
                hx-swap="innerHTML"
                hx-push-url="true"
                hx-history="false"
                hx-indicator=".htmx-indicator"
                hx-on:htmx:before-request=" document.getElementById('products-container').scrollIntoView({behavior: 'smooth'});"
                class="relative inline-flex items-center px-3 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
                aria-label="{% trans 'Previous page' %}">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                    <path fill-rule="evenodd" d="M7.72 12.53a.75.75 0 010-1.06l7.5-7.5a.75.75 0 111.06 1.06L9.31 12l6.97 6.97a.75.75 0 11-1.06 1.06l-7.5-7.5z" clip-rule="evenodd" />
                </svg>
                <span class="sr-only">{% trans 'Previous page' %}</span>
            </button>
        {% else %}
            <span class="relative inline-flex items-center px-3 py-2 rounded-l-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-500 cursor-not-allowed">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                    <path fill-rule="evenodd" d="M13.28 3.97a.75.75 0 010 1.06L6.31 12l6.97 6.97a.75.75 0 11-1.06 1.06l-7.5-7.5a.75.75 0 010-1.06l7.5-7.5a.75.75 0 011.06 0zm6 0a.75.75 0 010 1.06L12.31 12l6.97 6.97a.75.75 0 11-1.06 1.06l-7.5-7.5a.75.75 0 010-1.06l7.5-7.5a.75.75 0 011.06 0z" clip-rule="evenodd" />
                </svg>
                <span class="sr-only">{% trans 'First page' %}</span>
            </span>
            <span class="relative inline-flex items-center px-3 py-2 border border-gray-300 bg-gray-100 text-sm font-medium text-gray-500 cursor-not-allowed">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                    <path fill-rule="evenodd" d="M7.72 12.53a.75.75 0 010-1.06l7.5-7.5a.75.75 0 111.06 1.06L9.31 12l6.97 6.97a.75.75 0 11-1.06 1.06l-7.5-7.5z" clip-rule="evenodd" />
                </svg>
                <span class="sr-only">{% trans 'Previous page' %}</span>
            </span>
        {% endif %}

        {% for page in paginator.page_range %}
            {% if page_obj.number > 1 and page == page_obj.number|add:"-1" %}
                <button
                    hx-get="{{ page_url }}{{ page }}"
                    hx-target="#products-container"
                    hx-swap="innerHTML"
                    hx-push-url="true"
                    hx-history="false"
                    hx-indicator=".htmx-indicator"
                    hx-on:htmx:before-request=" document.getElementById('products-container').scrollIntoView({behavior: 'smooth'});"
                    class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                    {{ page }}
                </button>
            {% elif page_obj.number == page %}
                <span aria-current="page" class="z-10 relative inline-flex items-center px-4 py-2 border border-blue-500 bg-blue-50 text-sm font-medium text-blue-600">
                    {{ page }}
                </span>
            {% elif page_obj.number < paginator.num_pages and page == page_obj.number|add:"1" %}
                <button
                    hx-get="{{ page_url }}{{ page }}"
                    hx-target="#products-container"
                    hx-swap="innerHTML"
                    hx-push-url="true"
                    hx-history="false"
                    hx-indicator=".htmx-indicator"
                    hx-on:htmx:before-request=" document.getElementById('products-container').scrollIntoView({behavior: 'smooth'});"
                    class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                    {{ page }}
                </button>
            {% endif %}
        {% endfor %}

        {% if page_obj.has_next %}
            <button
                hx-get="{{ page_url }}{{ page_obj.next_page_number }}"
                hx-target="#products-container"
                hx-swap="innerHTML"
                hx-push-url="true"
                hx-history="false"
                hx-indicator=".htmx-indicator"
                hx-on:htmx:before-request=" document.getElementById('products-container').scrollIntoView({behavior: 'smooth'});"
                class="relative inline-flex items-center px-3 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
                aria-label="{% trans 'Next page' %}">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                    <path fill-rule="evenodd" d="M16.28 11.47a.75.75 0 010 1.06l-7.5 7.5a.75.75 0 01-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 011.06-1.06l7.5 7.5z" clip-rule="evenodd" />
                </svg>
                <span class="sr-only">{% trans 'Next page' %}</span>
            </button>
            <button
                hx-get="{{ page_url }}{{ paginator.num_pages }}"
                hx-target="#products-container"
                hx-swap="innerHTML"
                hx-push-url="true"
                hx-history="false"
                hx-indicator=".htmx-indicator"
                hx-on:htmx:before-request=" document.getElementById('products-container').scrollIntoView({behavior: 'smooth'});"
                class="relative inline-flex items-center px-3 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
                aria-label="{% trans 'Last page' %}">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                    <path fill-rule="evenodd" d="M4.72 3.97a.75.75 0 011.06 0l7.5 7.5a.75.75 0 010 1.06l-7.5 7.5a.75.75 0 01-1.06-1.06L11.69 12 4.72 5.03a.75.75 0 010-1.06zm6 0a.75.75 0 011.06 0l7.5 7.5a.75.75 0 010 1.06l-7.5 7.5a.75.75 0 11-1.06-1.06L17.69 12l-6.97-6.97a.75.75 0 010-1.06z" clip-rule="evenodd" />
                </svg>
                <span class="sr-only">{% trans 'Last page' %}</span>
            </button>
        {% else %}
            <span class="relative inline-flex items-center px-3 py-2 border border-gray-300 bg-gray-100 text-sm font-medium text-gray-500 cursor-not-allowed">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                    <path fill-rule="evenodd" d="M16.28 11.47a.75.75 0 010 1.06l-7.5 7.5a.75.75 0 01-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 011.06-1.06l7.5 7.5z" clip-rule="evenodd" />
                </svg>
                <span class="sr-only">{% trans 'Next page' %}</span>
            </span>
            <span class="relative inline-flex items-center px-3 py-2 rounded-r-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-500 cursor-not-allowed">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                    <path fill-rule="evenodd" d="M4.72 3.97a.75.75 0 011.06 0l7.5 7.5a.75.75 0 010 1.06l-7.5 7.5a.75.75 0 01-1.06-1.06L11.69 12 4.72 5.03a.75.75 0 010-1.06zm6 0a.75.75 0 011.06 0l7.5 7.5a.75.75 0 010 1.06l-7.5 7.5a.75.75 0 11-1.06-1.06L17.69 12l-6.97-6.97a.75.75 0 010-1.06z" clip-rule="evenodd" />
                </svg>
                <span class="sr-only">{% trans 'Last page' %}</span>
            </span>
        {% endif %}
    </nav>
</div>
{% endif %}
