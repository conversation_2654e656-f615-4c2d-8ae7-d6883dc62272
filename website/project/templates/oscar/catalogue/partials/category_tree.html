{% load i18n %}
{% load catalogue_tags %}

{% if category_tree %}
    <div class="side_categories bg-gray-100 p-4 rounded-lg shadow-sm mb-6">
        <div id="category-tree-container"
             x-data="categoryTreeComponent()"
             x-init="init()">
            <script type="application/json" id="category-tree-json">{{ category_tree_json|safe }}</script>
            {# This script is for initial Alpine.js load #}
            <script type="application/json" id="category-counts-json">{{ category_product_counts_json|safe }}</script>
            
            <!-- Search input -->
            <div class="mb-4 relative">
                <input type="text"
                       x-model.debounce.300ms="searchQuery"
                       placeholder="{% trans 'Quick search...' %}"
                       spellcheck="false"
                       class="w-full px-3 py-2 pr-10 border border-gray-300 rounded focus:outline-none focus:border-gray-400 focus:ring-1 focus:ring-gray-300 placeholder-gray-400 text-gray-700 transition"
                       x-ref="searchInput">
                <button x-show="searchQuery.length > 0"
                        @click="clearSearch"
                        type="button"
                        class="absolute right-0 top-0 bottom-0 my-auto h-full px-3 flex items-center justify-center bg-transparent border-0 text-gray-400 hover:text-gray-600 transition"
                        aria-label="Clear">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-5 h-5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <!-- Selected categories -->
            <div class="mb-3" x-show="$store.filters.state.categories.length > 0">
                <h6 class="text-sm font-semibold mb-1">{% trans "Selected categories" %}:</h6>
                <div class="flex flex-wrap gap-2">
                    <template x-for="cat in selectedCategories" :key="cat.id">
                        <div class="inline-flex items-center bg-gray-50 rounded-full px-3 py-1 shadow text-gray-800 text-sm font-medium transition">
                            <span class="mr-2" x-text="cat.name"></span>
                            <button @click="toggleCategory(cat)"
                                    class="flex items-center justify-center w-5 h-5 text-gray-400 hover:text-gray-600 hover:bg-gray-200 focus:outline-none ml-1 rounded-full transition"
                                    aria-label="Remove category">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2.2" stroke="currentColor" class="w-5 h-5">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                    </template>
                </div>
            </div>

            <!-- Category tree -->
            <ul class="space-y-1">
                <template x-for="cat in filteredCategories" :key="cat.id">
                    <li>
                        <div class="flex items-center py-1">
                            <div class="flex-shrink-0 w-[20px] mr-1">
                                <button x-show="cat.subcategories && cat.subcategories.length"
                                        @click="toggleExpanded(cat)"
                                        class="text-gray-500 hover:text-gray-700 hover:bg-gray-100 active:bg-gray-200 focus:outline-none flex items-center justify-center w-6 h-6 rounded transition-colors"
                                        :aria-label="isExpanded(cat) ? 'Collapse' : 'Expand'">
                                    <template x-if="isExpanded(cat)">
                                        <span class="text-gray-500 text-xl font-medium">-</span>
                                    </template>
                                    <template x-if="!isExpanded(cat)">
                                        <span class="text-gray-500 text-xl font-medium">+</span>
                                    </template>
                                </button>
                            </div>
                            <input type="checkbox"
                                   :id="'cat-' + cat.id"
                                   :checked="isSelected(cat)"
                                   @change="toggleCategory(cat)">
                            <label :for="'cat-' + cat.id" 
                                   @click.prevent.stop="toggleExpanded(cat)"
                                   class="cursor-pointer text-gray-700 break-words block flex-1 hover:text-gray-900"
                                   :class="{ 'bg-yellow-100': searchQuery && matchesSearch(cat) }"
                                   x-text="cat.name"></label>
                            <template x-if="getCount(cat) > 0">
                                <span :id="'category-count-' + cat.id"
                                      class="ml-2 bg-gray-200 text-gray-700 text-xs rounded-full px-2 py-0.5 flex-shrink-0 inline-block"
                                      x-text="getCount(cat)"></span>
                            </template>
                        </div>
                        <template x-if="isExpanded(cat) && cat.subcategories && cat.subcategories.length">
                            <ul class="ml-4 border-l border-gray-200 pl-4 space-y-1">
                                <template x-for="subcat in cat.subcategories" :key="subcat.id">
                                    <li>
                                        <div class="flex items-center py-1">
                                            <div class="flex-shrink-0 w-[20px] mr-1">
                                                <button x-show="subcat.subcategories && subcat.subcategories.length"
                                                        @click="toggleExpanded(subcat)"
                                                        class="text-gray-500 hover:text-gray-700 hover:bg-gray-100 active:bg-gray-200 focus:outline-none flex items-center justify-center w-6 h-6 rounded transition-colors"
                                                        :aria-label="isExpanded(subcat) ? 'Collapse' : 'Expand'">
                                                    <template x-if="isExpanded(subcat)">
                                                        <span class="text-gray-500 text-xl font-medium">-</span>
                                                    </template>
                                                    <template x-if="!isExpanded(subcat)">
                                                        <span class="text-gray-500 text-xl font-medium">+</span>
                                                    </template>
                                                </button>
                                            </div>
                                            <input type="checkbox"
                                                   :id="'cat-' + subcat.id"
                                                   :checked="isSelected(subcat)"
                                                   @change="toggleCategory(subcat)">
                                            <label :for="'cat-' + subcat.id"
                                                   @click.prevent.stop="toggleExpanded(subcat)"
                                                   class="cursor-pointer text-gray-700 break-words block flex-1 hover:text-gray-900"
                                                   :class="{ 'bg-yellow-100': searchQuery && matchesSearch(subcat) }"
                                                   x-text="subcat.name"></label>
                                            <template x-if="getCount(subcat) > 0">
                                                <span :id="'category-count-' + subcat.id"
                                                      class="ml-2 bg-gray-200 text-gray-700 text-xs rounded-full px-2 py-0.5 flex-shrink-0 inline-block"
                                                      x-text="getCount(subcat)"></span>
                                            </template>
                                        </div>
                                        <template x-if="isExpanded(subcat) && subcat.subcategories && subcat.subcategories.length">
                                            <ul class="ml-4 border-l border-gray-200 pl-4 space-y-1">
                                                <template x-for="subsubcat in subcat.subcategories" :key="subsubcat.id">
                                                    <li>
                                                        <div class="flex items-center py-1">
                                                            <div class="flex-shrink-0 w-[20px] mr-1">
                                                                <button x-show="subsubcat.subcategories && subsubcat.subcategories.length"
                                                                        @click="toggleExpanded(subsubcat)"
                                                                        class="text-gray-500 hover:text-gray-700 hover:bg-gray-100 active:bg-gray-200 focus:outline-none flex items-center justify-center w-6 h-6 rounded transition-colors"
                                                                        :aria-label="isExpanded(subsubcat) ? 'Collapse' : 'Expand'">
                                                                    <template x-if="isExpanded(subsubcat)">
                                                                        <span class="text-gray-500 text-xl font-medium">-</span>
                                                                    </template>
                                                                    <template x-if="!isExpanded(subsubcat)">
                                                                        <span class="text-gray-500 text-xl font-medium">+</span>
                                                                    </template>
                                                                </button>
                                                            </div>
                                                            <input type="checkbox"
                                                                   :id="'cat-' + subsubcat.id"
                                                                   :checked="isSelected(subsubcat)"
                                                                   @change="toggleCategory(subsubcat)">
                                                            <label :for="'cat-' + subsubcat.id"
                                                                   @click.prevent.stop="toggleExpanded(subsubcat)"
                                                                   class="cursor-pointer text-gray-700 break-words block flex-1 hover:text-gray-900"
                                                                   :class="{ 'bg-yellow-100': searchQuery && matchesSearch(subsubcat) }"
                                                                   x-text="subsubcat.name"></label>
                                                            <template x-if="getCount(subsubcat) > 0">
                                                                <span :id="'category-count-' + subsubcat.id"
                                                                      class="ml-2 bg-gray-200 text-gray-700 text-xs rounded-full px-2 py-0.5 flex-shrink-0 inline-block"
                                                                      x-text="getCount(subsubcat)"></span>
                                                            </template>
                                                        </div>
                                                        <template x-if="isExpanded(subsubcat) && subsubcat.subcategories && subsubcat.subcategories.length">
                                                            <ul class="ml-4 border-l border-gray-200 pl-4 space-y-1">
                                                                <template x-for="level4cat in subsubcat.subcategories" :key="level4cat.id">
                                                                    <li>
                                                                        <div class="flex items-center py-1">
                                                                            <div class="flex-shrink-0 w-[20px] mr-1">
                                                                                <button x-show="level4cat.subcategories && level4cat.subcategories.length"
                                                                                        @click="toggleExpanded(level4cat)"
                                                                                        class="text-gray-500 hover:text-gray-700 hover:bg-gray-100 active:bg-gray-200 focus:outline-none flex items-center justify-center w-6 h-6 rounded transition-colors"
                                                                                        :aria-label="isExpanded(level4cat) ? 'Collapse' : 'Expand'">
                                                                                    <template x-if="isExpanded(level4cat)">
                                                                                        <span class="text-gray-500 text-xl font-medium">-</span>
                                                                                    </template>
                                                                                    <template x-if="!isExpanded(level4cat)">
                                                                                        <span class="text-gray-500 text-xl font-medium">+</span>
                                                                                    </template>
                                                                                </button>
                                                                            </div>
                                                                            <input type="checkbox"
                                                                                   :id="'cat-' + level4cat.id"
                                                                                   :checked="isSelected(level4cat)"
                                                                                   @change="toggleCategory(level4cat)">
                                                                            <label :for="'cat-' + level4cat.id"
                                                                                   @click.prevent.stop="toggleExpanded(level4cat)"
                                                                                   class="cursor-pointer text-gray-700 break-words block flex-1 hover:text-gray-900"
                                                                                   :class="{ 'bg-yellow-100': searchQuery && matchesSearch(level4cat) }"
                                                                                   x-text="level4cat.name"></label>
                                                                            <template x-if="getCount(level4cat) > 0"><span :id="'category-count-' + level4cat.id" class="ml-2 bg-gray-200 text-gray-700 text-xs rounded-full px-2 py-0.5 flex-shrink-0 inline-block" x-text="getCount(level4cat)"></span></template>
                                                                        </div>
                                                                        <template x-if="isExpanded(level4cat) && level4cat.subcategories && level4cat.subcategories.length">
                                                                            <ul class="ml-4 border-l border-gray-200 pl-4 space-y-1">
                                                                                <template x-for="level5cat in level4cat.subcategories" :key="level5cat.id">
                                                                                    <li>
                                                                                        <div class="flex items-center py-1">
                                                                                            <div class="flex-shrink-0 w-[20px] mr-1">
                                                                                                {# No expander for level 5 assumed #}
                                                                                            </div>
                                                                                            <input type="checkbox" :id="'cat-' + level5cat.id" :checked="isSelected(level5cat)" @change="toggleCategory(level5cat)">
                                                                                            <label :for="'cat-' + level5cat.id" 
                                                                                                   @click.prevent.stop="toggleExpanded(level5cat)" 
                                                                                                   class="cursor-pointer text-gray-700 break-words block flex-1 hover:text-gray-900" 
                                                                                                   :class="{ 'bg-yellow-100': searchQuery && matchesSearch(level5cat) }" 
                                                                                                   x-text="level5cat.name"></label>
                                                                                            <template x-if="getCount(level5cat) > 0"><span :id="'category-count-' + level5cat.id" class="ml-2 bg-gray-200 text-gray-700 text-xs rounded-full px-2 py-0.5 flex-shrink-0 inline-block" x-text="getCount(level5cat)"></span></template>
                                                                                        </div>
                                                                                    </li>
                                                                                </template>
                                                                            </ul>
                                                                        </template>
                                                                    </li>
                                                                </template>
                                                            </ul>
                                                        </template>
                                                    </li>
                                                </template>
                                            </ul>
                                        </template>
                                    </li>
                                </template>
                            </ul>
                        </template>
                    </li>
                </template>
            </ul>

            {% comment %} OOB update for the counts JSON script. This script tag is swapped by HTMX. 
                        Alpine component listens for htmx:oobAfterSwap on its root ($el) 
                        and reloads its internal categoryCounts from this script. 
                        Alpine reactivity then updates the visible count spans. {% endcomment %}
            {% if is_htmx_request and category_counts_json %}
                <script type="application/json" id="category-counts-json" hx-swap-oob="true">{{ category_counts_json|safe }}</script>
            {% endif %}
        </div>
    </div>
{% endif %}

{# OOB-only version for HTMX responses (when only counts JSON is updated, not the full tree) #}
{# This is typically used when the view determines the main tree structure doesn't need to be sent, #}
{# but counts might have changed. #}
{% if is_htmx_request and not category_tree %}
    {% if category_counts_json %}
        <script type="application/json" id="category-counts-json" hx-swap-oob="true">{{ category_counts_json|safe }}</script>
    {% endif %}
{% endif %}