{% load i18n %}

<!-- This template contains ONLY the product listing section without any layout or navigation -->
<!-- It is used exclusively for HTMX responses -->

<!-- Script to ensure Alpine.js state is preserved during HTMX swaps -->
<script>
    // This script runs when the HTMX content is loaded
    document.addEventListener('htmx:afterSwap', function(event) {
        // Ensure the current viewMode from localStorage is applied to Alpine store
        if (typeof Alpine !== 'undefined' && Alpine.store('filters')) {
            const savedViewMode = localStorage.getItem('view_mode') || 'grid';
            Alpine.store('filters').state.viewMode = savedViewMode;

            // Synchronize Alpine.js state with URL parameters
            Alpine.store('filters').initFromUrl();
        }
    });
</script>

<!-- Simplified form without hidden fields as Alpine.js handles all parameters -->
<div class="flex flex-col md:flex-row justify-between items-center mb-4" x-cloak>
    {% if paginator.count %}
        <!-- Mobile: Results text with view toggle buttons -->
        <div class="flex flex-row justify-between items-start w-full md:w-auto mb-3 md:mb-0" x-cloak>
            <div class="text-gray-600 text-xs mr-4 break-words lg:text-sm" x-cloak>
                {% if paginator.num_pages > 1 %}
                    <strong>{{ paginator.count }}</strong> {% trans "results" %} - {% trans "showing" %} <strong>{{ page_obj.start_index }}</strong> {% trans "to" %} <strong>{{ page_obj.end_index }}</strong>.
                {% else %}
                    <strong>{{ paginator.count }}</strong> {% trans "results" %}.
                {% endif %}
            </div>

            <!-- View Toggle Buttons - visible only on mobile -->
            <div class="flex items-center space-x-2 ml-4 md:hidden flex-shrink-0" x-cloak>
                <button @click="Alpine.store('filters').toggleViewMode('grid')" class="p-2 rounded-md" :class="Alpine.store('filters').state.viewMode === 'grid' ? 'bg-gray-100 text-gray-500' : 'text-gray-500 hover:bg-gray-100'" aria-label="Grid View">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6A2.25 2.25 0 016 3.75h2.25A2.25 2.25 0 0110.5 6v2.25a2.25 2.25 0 01-2.25 2.25H6a2.25 2.25 0 01-2.25-2.25V6zM3.75 15.75A2.25 2.25 0 016 13.5h2.25a2.25 2.25 0 012.25 2.25V18a2.25 2.25 0 01-2.25 2.25H6A2.25 2.25 0 013.75 18v-2.25zM13.5 6a2.25 2.25 0 012.25-2.25H18A2.25 2.25 0 0120.25 6v2.25A2.25 2.25 0 0118 10.5h-2.25a2.25 2.25 0 01-2.25-2.25V6zM13.5 15.75a2.25 2.25 0 012.25-2.25H18a2.25 2.25 0 012.25 2.25V18A2.25 2.25 0 0118 20.25h-2.25A2.25 2.25 0 0113.5 18v-2.25z" />
                    </svg>
                </button>
                <button @click="Alpine.store('filters').toggleViewMode('list')" class="p-2 rounded-md" :class="Alpine.store('filters').state.viewMode === 'list' ? 'bg-gray-100 text-gray-500' : 'text-gray-500 hover:bg-gray-100'" aria-label="List View">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Desktop: Sorting options with view toggle buttons -->
        <div class="flex items-center w-full md:w-auto" x-cloak>
            <div class="flex items-center mr-4" x-cloak>
                <label for="per_page" class="mr-2 text-xs sm:text-sm text-gray-700">{% trans "Per page" %}:</label>
                <select name="per_page" id="per_page"
                        x-data
                        @change="Alpine.store('filters').state.per_page = $event.target.value; Alpine.store('filters').submitFilters()"
                        class="block w-auto px-2 py-2 text-xs sm:text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white cursor-pointer font-montserrat">
                    {% for option in per_page_options %}
                        <option value="{{ option }}" {% if option == current_per_page %}selected{% endif %} class="font-montserrat">
                            {{ option }}
                        </option>
                    {% endfor %}
                </select>
            </div>

            <label for="sort_by" class="mr-2 text-xs sm:text-sm text-gray-700">{% trans "Sort by" %}:</label>
            <select name="sort_by" id="sort_by"
                    x-data
                    @change="Alpine.store('filters').state.sort = $event.target.value; Alpine.store('filters').submitFilters()"
                    class="block w-full px-3 py-2 text-xs sm:text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white cursor-pointer font-montserrat">
                {% for option in sorting_options %}
                    <option value="{{ option.value }}" {% if option.value == current_sorting %}selected{% endif %} class="font-montserrat">
                        {{ option.label }}
                    </option>
                {% endfor %}
            </select>

            <!-- View Toggle Buttons - visible only on desktop -->
            <div class="hidden md:flex items-center space-x-2 ml-4" x-cloak>
                <button @click="Alpine.store('filters').toggleViewMode('grid')" class="p-2 rounded-md" :class="Alpine.store('filters').state.viewMode === 'grid' ? 'bg-gray-100 text-gray-500' : 'text-gray-500 hover:bg-gray-100'" aria-label="Grid View">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6A2.25 2.25 0 016 3.75h2.25A2.25 2.25 0 0110.5 6v2.25a2.25 2.25 0 01-2.25 2.25H6a2.25 2.25 0 01-2.25-2.25V6zM3.75 15.75A2.25 2.25 0 016 13.5h2.25a2.25 2.25 0 012.25 2.25V18a2.25 2.25 0 01-2.25 2.25H6A2.25 2.25 0 013.75 18v-2.25zM13.5 6a2.25 2.25 0 012.25-2.25H18A2.25 2.25 0 0120.25 6v2.25A2.25 2.25 0 0118 10.5h-2.25a2.25 2.25 0 01-2.25-2.25V6zM13.5 15.75a2.25 2.25 0 012.25-2.25H18a2.25 2.25 0 012.25 2.25V18A2.25 2.25 0 0118 20.25h-2.25A2.25 2.25 0 0113.5 18v-2.25z" />
                    </svg>
                </button>
                <button @click="Alpine.store('filters').toggleViewMode('list')" class="p-2 rounded-md" :class="Alpine.store('filters').state.viewMode === 'list' ? 'bg-gray-100 text-gray-500' : 'text-gray-500 hover:bg-gray-100'" aria-label="List View">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                    </svg>
                </button>
            </div>
        </div>
    {% else %}
        <p class="w-full text-gray-600 text-sm" x-cloak>
            {% trans "<strong>0</strong> results." %}
        </p>
    {% endif %}
</div>

{% if products %}
    <div x-bind:class="Alpine.store('filters').state.viewMode === 'grid' ? 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4' : 'flex flex-col gap-4'">
        {% for product in products %}
            {% include "oscar/catalogue/partials/product_unified.html" %}
        {% endfor %}
    </div>

    {% with page_obj=page %}
        {% include "oscar/catalogue/partials/pagination_storefront.html" %}
    {% endwith %}
{% else %}
    <p class="py-8 text-center text-gray-500">{% trans "No products found." %}</p>
{% endif %}
