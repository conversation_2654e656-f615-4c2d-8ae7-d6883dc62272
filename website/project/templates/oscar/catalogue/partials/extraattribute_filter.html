{% load i18n %}

<div id="extra-attributes-container" class="mb-6 mt-6" hx-swap-oob="true"
     x-data="{ showAll: false }">
    <h3 class="text-md font-semibold mb-2">{% trans "Extra attributes" %}</h3>
    <div class="flex flex-col gap-1 bg-gray-50 p-4 rounded-lg shadow-sm">
        {% if attribute_counts_with_products %}
            {% with initialCount=7 %}
                {# First 7 attributes are always visible #}
                {% for attr in attribute_counts %}
                    {% if forloop.counter0 < initialCount %}
                        <label class="flex items-center gap-2 cursor-pointer text-sm px-2 py-1 rounded hover:bg-gray-100">
                            <input type="checkbox"
                                value="{{ attr.id }}"
                                {% if attr.id in selected_extra_attributes %}checked{% endif %}
                                onchange="Alpine.store('filters').toggleExtraAttribute('{{ attr.id }}')"
                            >
                            <span>{{ attr.name }}</span>
                            <span class="ml-auto text-xs text-gray-500">{{ attr.count }}</span>
                        </label>
                    {% endif %}
                {% endfor %}

                {# Additional attributes are only visible when showAll=true #}
                <div x-show="showAll" style="display: none;">
                    {% for attr in attribute_counts %}
                        {% if forloop.counter0 >= initialCount %}
                            <label class="flex items-center gap-2 cursor-pointer text-sm px-2 py-1 rounded hover:bg-gray-100">
                                <input type="checkbox"
                                    value="{{ attr.id }}"
                                    {% if attr.id in selected_extra_attributes %}checked{% endif %}
                                    onchange="Alpine.store('filters').toggleExtraAttribute('{{ attr.id }}')"
                                >
                                <span>{{ attr.name }}</span>
                                <span class="ml-auto text-xs text-gray-500">{{ attr.count }}</span>
                            </label>
                        {% endif %}
                    {% endfor %}
                </div>

                {% if attribute_counts|length > initialCount %}
                    <button type="button"
                            class="mt-2 text-gray-600 hover:underline text-sm font-medium"
                            @click="showAll = !showAll"
                            x-init="remainingCount = {{ attribute_counts|length }} - {{ initialCount }}">
                        <span x-show="!showAll">{% trans "Show more" %} (<span x-text="remainingCount"></span>)</span>
                        <span x-show="showAll">{% trans "Show less" %}</span>
                    </button>
                {% endif %}
            {% endwith %}
        {% else %}
            <div class="text-sm text-gray-500 italic p-2">
                {% trans "No matching attributes for current filters" %}
            </div>
        {% endif %}
    </div>
</div>