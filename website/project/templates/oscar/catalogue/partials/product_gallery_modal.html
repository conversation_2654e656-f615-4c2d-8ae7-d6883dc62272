{% load i18n %}
{% load thumbnail %}
{% load static %}

<!-- Global Image Gallery Modal -->
<div
    x-data="productGalleryModal"
    @keydown.window="handleKeydown($event)"
    @open-gallery.window="openGalleryFromEvent($event)"
    id="global-gallery-modal"
    hx-preserve="true"
>
    <div
        x-show="showGallery"
        x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
        class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90"
        @click.self="closeGallery()"
        style="display: none;"
        tabindex="0"
    >
        <div class="relative w-full h-full flex flex-col">
            <!-- Close button -->
            <button
                @click="closeGallery()"
                class="absolute top-4 right-4 text-white hover:text-gray-300 z-50 focus:outline-none"
            >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>

            <!-- Main image container -->
            <div class="flex-grow flex items-center justify-center p-4">
                <!-- Loading indicator -->
                <div x-show="isLoading" class="text-white">
                    <div class="flex flex-col items-center">
                        <svg class="animate-spin h-10 w-10 text-white mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>{% trans "Loading images..." %}</span>
                    </div>
                </div>

                <!-- Images -->
                <div x-show="!isLoading">
                    <template x-for="(image, index) in images" :key="index">
                        <div
                            x-show="currentImageIndex === index"
                            class="max-w-full max-h-full"
                        >
                            <img
                                :src="image.url"
                                :alt="currentProduct ? currentProduct.title + ' - ' + (index + 1) : 'Product image'"
                                class="max-w-full max-h-[80vh] object-contain mx-auto"
                                @error="$event.target.src = '{% static 'oscar/img/image_not_found.jpg' %}'"
                            >
                        </div>
                    </template>
                </div>
            </div>

            <!-- Navigation buttons -->
            <template x-if="!isLoading && images.length > 1">
                <div>
                    <div class="absolute inset-y-0 left-4 flex items-center">
                        <button
                            @click.stop="prevImage()"
                            class="bg-white bg-opacity-50 hover:bg-opacity-70 rounded-full p-2 focus:outline-none"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                            </svg>
                        </button>
                    </div>

                    <div class="absolute inset-y-0 right-4 flex items-center">
                        <button
                            @click.stop="nextImage()"
                            class="bg-white bg-opacity-50 hover:bg-opacity-70 rounded-full p-2 focus:outline-none"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </button>
                    </div>
                </div>
            </template>

            <!-- Thumbnails -->
            <template x-if="!isLoading && images.length > 1">
                <div class="flex justify-center gap-2 p-4 bg-black bg-opacity-50">
                    <template x-for="(image, index) in images" :key="index">
                        <button
                            @click="currentImageIndex = index"
                            class="w-16 h-12 overflow-hidden rounded border-2 transition-all duration-200 focus:outline-none"
                            :class="currentImageIndex === index ? 'border-blue-500' : 'border-transparent hover:border-gray-300'"
                        >
                            <img
                                :src="image.thumbnail"
                                :alt="currentProduct ? currentProduct.title + ' - Thumbnail ' + (index + 1) : 'Thumbnail'"
                                class="w-full h-full object-cover"
                                @error="$event.target.src = '{% static 'oscar/img/image_not_found.jpg' %}'"
                            >
                        </button>
                    </template>
                </div>
            </template>
        </div>
    </div>
</div>
