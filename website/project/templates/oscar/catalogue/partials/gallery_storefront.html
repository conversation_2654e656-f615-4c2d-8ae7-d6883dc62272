{% load image_tags %}
{% load i18n %}
{% load custom_purchase_info_tags %}
{% load static %}

{% purchase_info_for_product request product as session %}
{% with all_images=product.images.all %}

<div class="product-gallery" x-data="productGallery()" x-init="init()">
    {% if all_images|length > 1 %}
        <!-- Main Image Display -->
        <div class="relative bg-gray-50 rounded-lg overflow-hidden mb-4">
            <div class="aspect-w-4 aspect-h-3">
                <template x-for="(image, index) in images" :key="index">
                    <div x-show="currentIndex === index" 
                         class="absolute inset-0 transition-opacity duration-300">
                        <img :src="image.thumbLarge" 
                             :alt="image.alt" 
                             class="w-full h-full object-contain cursor-pointer transition-transform duration-200"
                             @click="openModal(index)">
                    </div>
                </template>
            </div>
            
            <!-- Navigation Arrows -->
            <button @click="previousImage()" 
                    class="absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-2 shadow-md transition-all duration-200">
                <svg class="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
            </button>
            <button @click="nextImage()" 
                    class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-2 shadow-md transition-all duration-200">
                <svg class="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>
        
        <!-- Thumbnail Navigation -->
        <div class="flex space-x-2 overflow-x-auto p-1.5">
            <template x-for="(image, index) in images" :key="index">
                <button @click="setCurrentImage(index)" 
                        :class="{ 'ring-2 ring-blue-500': currentIndex === index }"
                        class="flex-shrink-0 w-16 h-16 bg-gray-100 rounded-md overflow-hidden hover:ring-2 hover:ring-gray-300 transition-all duration-200">
                    <img :src="image.thumbSmall" 
                         :alt="image.alt" 
                         class="w-full h-full object-cover">
                </button>
            </template>
        </div>
        
    {% else %}
        <!-- Single Image Display -->
        <div class="relative bg-gray-50 rounded-lg overflow-hidden">
            <div class="aspect-w-4 aspect-h-3">
                {% with image=product.primary_image %}
                    {% oscar_thumbnail image.original "800x600" upscale=False crop="center" quality=95 as thumb_large %}
                    <img src="{{ thumb_large.url }}" 
                         alt='{{ product.get_title }}' 
                         class="w-full h-full object-contain cursor-pointer transition-transform duration-200"
                         @click="openModal(0)">
                {% endwith %}
            </div>
        </div>
    {% endif %}
    
    <!-- Modal for Full Size Images -->
    <div x-show="showModal" 
         x-cloak
         class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75"
         @click="closeModal()"
         @keydown.window="handleKeydown($event)">
        <div class="relative max-w-4xl max-h-full p-4" @click.stop>
            <!-- Loading indicator -->
            <div x-show="isLoadingOriginal" class="flex items-center justify-center h-96">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
            </div>
            
            <!-- Original image -->
            <img x-show="!isLoadingOriginal && currentOriginalSrc" 
                 :src="currentOriginalSrc" 
                 :alt="images[modalIndex]?.alt"
                 class="max-w-full max-h-full object-contain">
            
            <!-- Modal Navigation -->
            <button @click="previousModalImage()" 
                    x-show="images.length > 1"
                    class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-3 shadow-lg">
                <svg class="w-6 h-6 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
            </button>
            <button @click="nextModalImage()" 
                    x-show="images.length > 1"
                    class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-3 shadow-lg">
                <svg class="w-6 h-6 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
            </button>
            
            <!-- Close Button -->
            <button @click="closeModal()" 
                    class="absolute top-4 right-4 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-2 shadow-lg">
                <svg class="w-6 h-6 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>
    </div>
</div>

<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('productGallery', () => ({
        currentIndex: 0,
        modalIndex: 0,
        showModal: false,
        isLoadingOriginal: false,
        currentOriginalSrc: null,
        images: [
            {% for image in all_images %}
            {
                {% oscar_thumbnail image.original "800x600" upscale=False crop="center" quality=95 as thumb_large %}
                {% oscar_thumbnail image.original "65x55" crop="center" as thumb_small %}
                thumbLarge: '{{ thumb_large.url }}',
                thumbSmall: '{{ thumb_small.url }}',
                originalUrl: '{{ image.original.url }}', // Store URL but don't preload
                alt: '{{ product.get_title }} - {% trans "Image" %} {{ forloop.counter }}'
            }{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        
        init() {
            // Initialize gallery
        },
        
        setCurrentImage(index) {
            this.currentIndex = index;
        },
        
        nextImage() {
            this.currentIndex = (this.currentIndex + 1) % this.images.length;
        },
        
        previousImage() {
            this.currentIndex = this.currentIndex === 0 ? this.images.length - 1 : this.currentIndex - 1;
        },
        
        async openModal(index) {
            this.modalIndex = index;
            this.showModal = true;
            document.body.style.overflow = 'hidden';
            
            // Load original image lazily
            await this.loadOriginalImage(index);
        },
        
        closeModal() {
            this.showModal = false;
            this.currentOriginalSrc = null;
            document.body.style.overflow = '';
        },
        
        async nextModalImage() {
            const newIndex = (this.modalIndex + 1) % this.images.length;
            this.modalIndex = newIndex;
            await this.loadOriginalImage(newIndex);
        },
        
        async previousModalImage() {
            const newIndex = this.modalIndex === 0 ? this.images.length - 1 : this.modalIndex - 1;
            this.modalIndex = newIndex;
            await this.loadOriginalImage(newIndex);
        },
        
        async loadOriginalImage(index) {
            const imageUrl = this.images[index].originalUrl;
            
            // Show loading indicator
            this.isLoadingOriginal = true;
            this.currentOriginalSrc = null;
            
            try {
                // Preload the image (Apache cache will handle subsequent loads)
                const img = new Image();
                img.onload = () => {
                    this.currentOriginalSrc = imageUrl;
                    this.isLoadingOriginal = false;
                };
                img.onerror = () => {
                    console.error('Failed to load original image:', imageUrl);
                    // Fallback to thumbnail if original fails
                    this.currentOriginalSrc = this.images[index].thumbLarge;
                    this.isLoadingOriginal = false;
                };
                img.src = imageUrl;
            } catch (error) {
                console.error('Error loading original image:', error);
                this.currentOriginalSrc = this.images[index].thumbLarge;
                this.isLoadingOriginal = false;
            }
        },
        
        handleKeydown(event) {
            if (this.showModal) {
                if (event.key === 'Escape') {
                    this.closeModal();
                } else if (event.key === 'ArrowRight' || event.key === 'd') {
                    this.nextModalImage();
                } else if (event.key === 'ArrowLeft' || event.key === 'a') {
                    this.previousModalImage();
                } else if (event.key === 'ArrowUp' || event.key === 'w') {
                    this.previousModalImage();
                } else if (event.key === 'ArrowDown' || event.key === 's') {
                    this.nextModalImage();
                } else if (event.key === ' ') {
                    event.preventDefault();
                    this.nextModalImage();
                }
            }
        }
    }));
});
</script>

{% endwith %}
