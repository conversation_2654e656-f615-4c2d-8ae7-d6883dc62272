{% load custom_currency_filters %}
{% load i18n %}

{# Assume 'product' object with 'purchase_info' attribute is available in context #}
{% with session=product.purchase_info %}
    {% if session and session.price.exists %}
        <div class="flex flex-col items-end">
            <span class="text-lg font-bold text-gray-900">
            {% if session.price.is_tax_known %}
                {{ session.price.incl_tax|user_currency:user_currency }}
            {% else %}
                {{ session.price.excl_tax|user_currency:user_currency }}
            {% endif %}
            </span>
            {% if session.stockrecord %}
                <span class="text-sm {% if session.stockrecord.net_stock_level > 0 %}text-green-600{% else %}text-red-600{% endif %}">
                    {% if session.stockrecord.net_stock_level > 0 %}
                        {% trans "In stock" %}: {{ session.stockrecord.net_stock_level }}
                        {% if product.condition == 'defect' or product.condition == 'broken' %}
                            <svg class="inline w-4 h-4 text-red-500 ml-1 align-text-bottom" fill="none" stroke="currentColor" viewBox="0 0 24 24" role="img" aria-label="{% trans 'With defect' %}">
                                <title>{% trans "With defect" %}</title>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                        {% endif %}
                    {% else %}
                        {% trans "Out of stock" %}
                    {% endif %}
                </span>
            {% endif %}
        </div>
    {% else %}
        {# Optionally show placeholder if price doesn't exist #}
         <span class="text-lg font-bold text-gray-500">{% trans 'N/A' %}</span>
    {% endif %}
{% endwith %}
