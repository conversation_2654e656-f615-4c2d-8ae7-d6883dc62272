{% load i18n %}

<div id="range-filters-container" class="mb-6"
    x-data="{
        yearFrom: $store.filters.state.year_range.from || '',
        yearTo: $store.filters.state.year_range.to || '',
        priceFrom: $store.filters.state.price_range.from || '',
        priceTo: $store.filters.state.price_range.to || ''
    }"
    @filters-cleared.window="
        yearFrom = '';
        yearTo = '';
        priceFrom = '';
        priceTo = '';
    ">
    <h3 class="text-md font-semibold mb-2">{% trans "Additional Filters" %}</h3>
    <div class="flex flex-col gap-4 bg-gray-50 p-4 rounded-lg shadow-sm">
        <!-- Year Range Filter -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Year Range" %}</label>
            <div class="flex items-center gap-2">
                <div class="w-1/2 relative">
                    <label for="year_from" class="sr-only">{% trans "From" %}</label>
                    <input
                        type="text"
                        id="year_from"
                        x-model="yearFrom"
                        placeholder="{% trans 'From' %}"
                        :class="yearFrom ? 'block w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-blue-950 text-white' : 'block w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'"
                        @input="yearFrom = yearFrom.replace(/[^0-9]/g, ''); if(yearFrom.length > 4) yearFrom = yearFrom.substring(0, 4);"
                        @blur="$store.filters.setYearRange(yearFrom, yearTo);"
                        @keyup.enter="$store.filters.setYearRange(yearFrom, yearTo);"
                        maxlength="4"
                        pattern="[0-9]{4}"
                    >
                    <button
                        x-cloak
                        x-bind:class="yearFrom ? 'absolute inset-y-0 right-0 pr-3 flex items-center text-white hover:text-gray-200' : 'hidden'"
                        @click="yearFrom = ''; $store.filters.setYearRange('', yearTo);"
                        type="button"

                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="w-1/2 relative">
                    <label for="year_to" class="sr-only">{% trans "To" %}</label>
                    <input
                        type="text"
                        id="year_to"
                        x-model="yearTo"
                        placeholder="{% trans 'To' %}"
                        :class="yearTo ? 'block w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-blue-950 text-white' : 'block w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'"
                        @input="yearTo = yearTo.replace(/[^0-9]/g, ''); if(yearTo.length > 4) yearTo = yearTo.substring(0, 4);"
                        @blur="$store.filters.setYearRange(yearFrom, yearTo);"
                        @keyup.enter="$store.filters.setYearRange(yearFrom, yearTo);"
                        maxlength="4"
                        pattern="[0-9]{4}"
                    >
                    <button
                        x-cloak
                        x-bind:class="yearTo ? 'absolute inset-y-0 right-0 pr-3 flex items-center text-white hover:text-gray-200' : 'hidden'"
                        @click="yearTo = ''; $store.filters.setYearRange(yearFrom, '');"
                        type="button"

                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>

        </div>

        <!-- Price Range Filter -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Price Range" %}</label>
            <div class="flex items-center gap-2">
                <div class="w-1/2 relative">
                    <label for="price_from" class="sr-only">{% trans "From" %}</label>
                    <input
                        type="text"
                        id="price_from"
                        x-model="priceFrom"
                        placeholder="{% trans 'From' %}"
                        :class="priceFrom ? 'block w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-blue-950 text-white' : 'block w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'"
                        @input="priceFrom = priceFrom.replace(/[^0-9.]/g, ''); if((priceFrom.match(/\./g) || []).length > 1) priceFrom = priceFrom.substring(0, priceFrom.lastIndexOf('.'));"
                        @blur="$store.filters.setPriceRange(priceFrom, priceTo);"
                        @keyup.enter="$store.filters.setPriceRange(priceFrom, priceTo);"
                    >
                    <button
                        x-cloak
                        x-bind:class="priceFrom ? 'absolute inset-y-0 right-0 pr-3 flex items-center text-white hover:text-gray-200' : 'hidden'"
                        @click="priceFrom = ''; $store.filters.setPriceRange('', priceTo);"
                        type="button"

                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="w-1/2 relative">
                    <label for="price_to" class="sr-only">{% trans "To" %}</label>
                    <input
                        type="text"
                        id="price_to"
                        x-model="priceTo"
                        placeholder="{% trans 'To' %}"
                        :class="priceTo ? 'block w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-blue-950 text-white' : 'block w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'"
                        @input="priceTo = priceTo.replace(/[^0-9.]/g, ''); if((priceTo.match(/\./g) || []).length > 1) priceTo = priceTo.substring(0, priceTo.lastIndexOf('.'));"
                        @blur="$store.filters.setPriceRange(priceFrom, priceTo);"
                        @keyup.enter="$store.filters.setPriceRange(priceFrom, priceTo);"
                    >
                    <button
                        x-cloak
                        x-bind:class="priceTo ? 'absolute inset-y-0 right-0 pr-3 flex items-center text-white hover:text-gray-200' : 'hidden'"
                        @click="priceTo = ''; $store.filters.setPriceRange(priceFrom, '');"
                        type="button"

                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>

        </div>
    </div>
</div>
