{% extends "oscar/layout_storefront.html" %}
{% load i18n %}
{% load promotion_tags %}

{% comment %}
Keep the same header and footer as the standard layout template but override the 
main content area to split it into a sidebar and a content block
{% endcomment %}
{% block content_wrapper %}
<div class="content">
    <div class="row">
        <aside class="sidebar col-sm-4 col-md-3">
            {% block column_left %}{% endblock %}
        </aside>

        <div class="col-sm-8 col-md-9">
            {% block subheader %}{% endblock subheader %}

            {# Render promotions only if not disabled #}
            {% if not disable_promotions %}
            <div id="promotions">
                {% for promotion in promotions_page %}
                    {% render_promotion promotion %}
                {% endfor %}
            </div>
            {% endif %}

            {# Div exists for AJAX updates to entire content section #}
            <div id="content_inner">{% block content %}{% endblock %}</div>
        </div>
    </div>
</div>
{% endblock %} 